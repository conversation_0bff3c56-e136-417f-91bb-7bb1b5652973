const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');
const { uploadFile } = require('../services/csv.service');
const models = require('../models');

/**
 * Transforms object properties using template strings and context data
 * @param {Object} template - Template object with placeholder strings
 * @param {Object} data - Data object to extract values from
 * @param {Object} context - Additional context data
 * @returns {Object} Transformed object
 */
function transformObject(template, data, context = {}) {
  if (typeof template === 'string') {
    // Handle template strings like {{Identity.email}}
    return template.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const keys = path.split('.');
      let value = data;

      // Handle special context variables
      if (keys[0] === 'current_timestamp') {
        return new Date().toISOString();
      }
      if (keys[0] === 'batch_id') {
        return context.batchId || uuidv4();
      }
      if (keys[0] === 'batch_size') {
        return context.batchSize || 1;
      }

      // Handle Identity.field_name pattern - skip model name and use actual field
      if (keys[0] === 'Identity' && keys.length > 1) {
        const actualFieldName = keys[1];
        value = data[actualFieldName];
        return value || '';
      }

      // Navigate through the object path for other cases
      for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return ''; // Return empty string if path doesn't exist
        }
      }

      return value || '';
    });
  }

  if (Array.isArray(template)) {
    return template.map(item => transformObject(item, data, context));
  }

  if (typeof template === 'object' && template !== null) {
    const result = {};
    for (const [key, value] of Object.entries(template)) {
      result[key] = transformObject(value, data, context);
    }
    return result;
  }

  return template;
}

/**
 * Generates XML content from transformed data
 * @param {Object} data - Transformed data object
 * @param {Object} xmlConfig - XML configuration from mapping
 * @returns {string} Generated XML content
 */
function generateXmlContent(data, xmlConfig) {
  const { rootElement, xmlDeclaration, namespace } = xmlConfig;

  let xml = '';

  // Add XML declaration
  if (xmlDeclaration) {
    xml += `<?xml version="${xmlDeclaration.version || '1.0'}" encoding="${xmlDeclaration.encoding || 'UTF-8'}"?>\n`;
  }

  // Add root element with namespace if specified
  const rootAttrs = namespace ? ` xmlns="${namespace}"` : '';
  xml += `<${rootElement}${rootAttrs}>\n`;

  // Generate XML content recursively
  xml += generateXmlElements(data, 1);

  xml += `</${rootElement}>\n`;

  return xml;
}

/**
 * Recursively generates XML elements from data object
 * @param {*} data - Data to convert to XML
 * @param {number} indent - Current indentation level
 * @returns {string} XML elements
 */
function generateXmlElements(data, indent = 0) {
  const indentStr = '  '.repeat(indent);
  let xml = '';

  if (Array.isArray(data)) {
    // Handle arrays
    data.forEach(item => {
      xml += generateXmlElements(item, indent);
    });
  } else if (typeof data === 'object' && data !== null) {
    // Handle objects
    Object.entries(data).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // Handle array values
        value.forEach(item => {
          xml += `${indentStr}<${key}>\n`;
          xml += generateXmlElements(item, indent + 1);
          xml += `${indentStr}</${key}>\n`;
        });
      } else if (typeof value === 'object' && value !== null) {
        // Handle nested objects
        xml += `${indentStr}<${key}>\n`;
        xml += generateXmlElements(value, indent + 1);
        xml += `${indentStr}</${key}>\n`;
      } else {
        // Handle primitive values
        const escapedValue = escapeXml(String(value || ''));
        xml += `${indentStr}<${key}>${escapedValue}</${key}>\n`;
      }
    });
  }

  return xml;
}

/**
 * Escapes XML special characters
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeXml(text) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

/**
 * Validates transformed data against validation rules, including support for array fields.
 * @param {Object} data - Data to validate (with array roots like Personnel, Credentials, Clearances)
 * @param {Object} validation - Validation configuration
 * @returns {Object} Validation result with isValid and errors[]
 */
function validateData(data, validation) {
  const errors = [];

  if (!validation) {
    return { isValid: true, errors: [] };
  }

  // Helper to validate one value against rules
  const applyRules = (value, fieldPath, rules) => {
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors.push(`Required field '${fieldPath}' is missing or empty`);
    }
    if (value !== undefined && value !== null && value !== '') {
      if (rules.type === 'email' && !isValidEmail(value)) {
        errors.push(`Field '${fieldPath}' must be a valid email address`);
      }
      if (rules.type === 'string' && typeof value !== 'string') {
        errors.push(`Field '${fieldPath}' must be a string`);
      }
      if (rules.minLength && String(value).length < rules.minLength) {
        errors.push(`Field '${fieldPath}' must be at least ${rules.minLength} characters long`);
      }
      if (rules.maxLength && String(value).length > rules.maxLength) {
        errors.push(`Field '${fieldPath}' must be no more than ${rules.maxLength} characters long`);
      }
    }
  };

  // 1. Check required fields (handles arrays)
  if (validation.required) {
    validation.required.forEach(fieldPath => {
      const [root, ...rest] = fieldPath.split('.');
      const subPath = rest.join('.');
      const rootVal = data[root];

      if (Array.isArray(rootVal)) {
        rootVal.forEach((item, idx) => {
          const v = subPath ? getNestedValue(item, subPath) : item;
          if (v === undefined || v === null || v === '') {
            errors.push(`Required field '${root}[${idx}].${subPath}' is missing or empty`);
          }
        });
      } else {
        const v = getNestedValue(data, fieldPath);
        if (v === undefined || v === null || v === '') {
          errors.push(`Required field '${fieldPath}' is missing or empty`);
        }
      }
    });
  }

  // 2. Check field rules (handles arrays)
  if (validation.rules) {
    Object.entries(validation.rules).forEach(([fieldPath, rules]) => {
      const [root, ...rest] = fieldPath.split('.');
      const subPath = rest.join('.');
      const rootVal = data[root];

      if (Array.isArray(rootVal)) {
        rootVal.forEach(item => {
          const v = subPath ? getNestedValue(item, subPath) : item;
          applyRules(v, fieldPath, rules);
        });
      } else {
        const v = getNestedValue(data, fieldPath);
        applyRules(v, fieldPath, rules);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets nested value from object using dot notation
 * @param {Object} obj - Object to search in
 * @param {string} path - Dot notation path
 * @returns {*} Found value or undefined
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Collection-based XML generation handler that processes multiple events in batches
 * @param {Object} params - Handler parameters
 * @param {Array} params.eventCollection - Collection of events to process
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Collection processing results
 */
const generateXml = async ({ eventCollection, agent, mappingConfig, performanceMonitor = null }) => {
  const collectionResults = {
    agentName: agent.name,
    totalEvents: eventCollection.length,
    totalRecords: 0,
    successfulRecords: 0,
    failedRecords: 0,
    filesGenerated: 0,
    errors: [],
    processedFiles: [],
    messageResults: [] // Track individual message success/failure
  };

  try {
    logger.info(`[XML Collection Handler] Starting collection processing for agent: ${agent.name}`, { eventCount: eventCollection.length });

    // Step 1: Load mapping configuration once for the entire collection
    performanceMonitor?.startStep('Load Mapping Configuration', { agentName: agent.name });
    performanceMonitor?.endStep('Load Mapping Configuration', {});

    logger.info(`[XML Collection Handler] Loaded mapping configuration: ${agent.mapping}`, {});

    // Step 2: Process each event individually to generate separate XML files
    performanceMonitor?.startStep('Process Individual Events', { eventCount: eventCollection.length });

    if (eventCollection.length === 0) {
      logger.warn(`[XML Collection Handler] No events found in collection for agent: ${agent.name}`, {});
      return collectionResults;
    }

    let totalRecords = 0;
    const errors = [];

    // Step 1: Extract all parent_ids from events for bulk fetching
    performanceMonitor?.startStep('Extract Parent IDs for XML', { eventCount: eventCollection.length });
    const parentIds = [];
    const eventToParentIdMapping = new Map(); // Track which parent_id belongs to which event

    for (let eventIndex = 0; eventIndex < eventCollection.length; eventIndex++) {
      const { event, messageId } = eventCollection[eventIndex];
      const parentId = event.parent_id;
      if (parentId) {
        parentIds.push(parentId);
        eventToParentIdMapping.set(eventIndex, { parentId, messageId });
      }
    }

    performanceMonitor?.endStep('Extract Parent IDs for XML', { totalParentIds: parentIds.length });

    if (parentIds.length === 0) {
      logger.warn(`[XML Collection Handler] No parent_ids found in event collection for agent: ${agent.name}`, {});
      return collectionResults;
    }

    // Step 2: Bulk fetch data from the model specified in agent.schema
    performanceMonitor?.startStep('Bulk Fetch Model Data for XML', { modelName: agent.schema, parentIds: parentIds.length });
    const model = models[agent.schema];
    if (!model) {
      const error = `Model '${agent.schema}' not found in models`;
      logger.error(`[XML Collection Handler] ${error}`, {});
      collectionResults.errors.push(error);
      return collectionResults;
    }

    let fetchedRecords = [];
    try {
      const primaryKeyField = model.primaryKeyAttribute || 'id';
      const records = await model.findAll({
        where: {
          [primaryKeyField]: parentIds
        }
      });

      // Convert Sequelize instances to plain objects and create lookup map
      fetchedRecords = records.map(record => record.toJSON());

      logger.info(`[XML Collection Handler] Fetched ${fetchedRecords.length} records from ${agent.schema} model`, {});
    } catch (error) {
      logger.error(`[XML Collection Handler] Error fetching data from ${agent.schema} model:`, error);
      collectionResults.errors.push(`Failed to fetch data from ${agent.schema}: ${error.message}`);
      return collectionResults;
    }

    // Create lookup map for quick access to records by primary key
    const recordLookup = new Map();
    const primaryKeyField = model.primaryKeyAttribute || 'id';
    fetchedRecords.forEach(record => {
      recordLookup.set(record[primaryKeyField], record);
    });

    performanceMonitor?.endStep('Bulk Fetch Model Data for XML', { totalRecords: fetchedRecords.length });

    // Step 3: Process each event separately with fetched data
    for (let eventIndex = 0; eventIndex < eventCollection.length; eventIndex++) {
      const eventMapping = eventToParentIdMapping.get(eventIndex);
      if (!eventMapping) {
        continue; // Skip events without parent_id
      }

      const { parentId, messageId } = eventMapping;
      const recordsInEvent = recordLookup.get(parentId);

      if (!recordsInEvent) {
        logger.warn(`[XML Collection Handler] No data found for parent_id: ${parentId}`, {});
        continue;
      }

      // Convert single record to array for consistent processing
      const recordsArray = [recordsInEvent];
      totalRecords += recordsArray.length;

      try {
        // Generate unique filename for this event
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${agent.name}_event_${eventIndex + 1}_${timestamp}.xml`;

        await processEventToXml({
          records: recordsArray,
          filename,
          messageId,
          eventIndex,
          agent,
          mappingConfig,
          performanceMonitor,
          collectionResults
        });

      } catch (error) {
        logger.error(`[XML Collection Handler] Failed to process event ${eventIndex + 1}:`, error.message);
        errors.push(`Event ${eventIndex + 1}: ${error.message}`);

        // Mark this message as failed
        collectionResults.messageResults.push({
          messageId,
          success: false,
          error: error.message,
          eventIndex
        });
        collectionResults.failedRecords += recordsInEvent.length;
      }
    }

    collectionResults.totalRecords = totalRecords;
    collectionResults.errors.push(...errors);
    performanceMonitor?.endStep('Process Individual Events', { totalRecords, filesGenerated: collectionResults.filesGenerated });

    logger.info(`[XML Collection Handler] Collection processing completed for agent: ${agent.name}`, {
      totalEvents: collectionResults.totalEvents,
      totalRecords: collectionResults.totalRecords,
      successfulRecords: collectionResults.successfulRecords,
      filesGenerated: collectionResults.filesGenerated
    });

    return collectionResults;

  } catch (error) {
    logger.error(`[XML Collection Handler] Error processing collection for agent: ${agent.name}:`, error.message, {});
    collectionResults.errors.push(error.message);
    collectionResults.failedRecords = collectionResults.totalRecords;
    return collectionResults;
  }
};

/**
 * Processes a single event and generates an XML file
 * @param {Object} params - Processing parameters
 * @param {Array} params.records - Records from the event
 * @param {string} params.filename - Output filename
 * @param {string} params.messageId - Message ID for tracking
 * @param {number} params.eventIndex - Event index
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.mappingConfig - Mapping configuration
 * @param {Object} params.performanceMonitor - Performance monitor
 * @param {Object} params.collectionResults - Collection results to update
 */
async function processEventToXml({ records, filename, messageId, eventIndex, agent, mappingConfig, performanceMonitor, collectionResults }) {
  const batchId = uuidv4();
  const context = {
    batchId,
    batchSize: records.length,
    timestamp: new Date().toISOString()
  };

  performanceMonitor?.startStep(`Transform Event ${eventIndex + 1} Data`, { recordCount: records.length });

  // Transform data according to mapping
  const transformedData = {};
  const transformationErrors = [];

  // Process each section of the XML mapping
  for (const [sectionKey, sectionTemplate] of Object.entries(mappingConfig.dataTransform)) {
    if (Array.isArray(sectionTemplate)) {
      // Handle array sections (like Personnel, Credentials, Clearances)
      transformedData[sectionKey] = records.map((record, recordIndex) => {
        try {
          // Pass the record directly since we fixed the transformObject function
          const transformedRecord = transformObject(sectionTemplate[0], record, context);
          return transformedRecord;
        } catch (error) {
          transformationErrors.push(`Record ${recordIndex} in event ${eventIndex + 1} transformation failed: ${error.message}`);
          return null; // Return null for failed transformations
        }
      }).filter(record => record !== null); // Remove failed transformations
    } else {
      // Handle single object sections (like ImportHeader, ImportFooter)
      try {
        transformedData[sectionKey] = transformObject(sectionTemplate, {}, context);
      } catch (error) {
        transformationErrors.push(`Section ${sectionKey} in event ${eventIndex + 1} transformation failed: ${error.message}`);
      }
    }
  }

  if (transformationErrors.length > 0) {
    collectionResults.errors.push(...transformationErrors);
  }

  performanceMonitor?.endStep(`Transform Event ${eventIndex + 1} Data`, {
    sectionsProcessed: Object.keys(transformedData).length,
    transformationErrors: transformationErrors.length
  });

  // Validate transformed data
  performanceMonitor?.startStep(`Validate Event ${eventIndex + 1} Data`, {});
  const validationResult = validateData(transformedData, mappingConfig.validation);
  if (!validationResult.isValid) {
    const errorMessage = `Event ${eventIndex + 1} validation failed: ${validationResult.errors.join(', ')}`;
    collectionResults.errors.push(errorMessage);

    // Mark this message as failed
    collectionResults.messageResults.push({
      messageId,
      success: false,
      error: errorMessage,
      eventIndex
    });
    collectionResults.failedRecords += records.length;
    performanceMonitor?.endStep(`Validate Event ${eventIndex + 1} Data`, { success: false, validationErrors: validationResult.errors.length });
    return;
  }
  performanceMonitor?.endStep(`Validate Event ${eventIndex + 1} Data`, { success: true });

  // Generate XML file
  performanceMonitor?.startStep(`Generate Event ${eventIndex + 1} XML File`, {});

  try {
    const xmlContent = generateXmlContent(transformedData, mappingConfig.xmlConfig);

    const tempDir = './downloads/temp';
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    const tempFilePath = path.join(tempDir, filename);

    fs.writeFileSync(tempFilePath, xmlContent, 'utf8');
    const fileStats = fs.statSync(tempFilePath);
    performanceMonitor?.endStep(`Generate Event ${eventIndex + 1} XML File`, { fileSize: fileStats.size });

    logger.info(`[XML Collection Handler] Generated XML file for event ${eventIndex + 1}: ${tempFilePath}`, { fileSize: fileStats.size });

    // Upload file to destination
    performanceMonitor?.startStep(`Upload Event ${eventIndex + 1} XML`, { destination: agent.source });
    const uploadResult = await uploadFile(tempFilePath, agent, performanceMonitor, 'XML');
    performanceMonitor?.endStep(`Upload Event ${eventIndex + 1} XML`, { success: uploadResult.success });

    // Clean up temporary file
    try {
      fs.unlinkSync(tempFilePath);
      logger.info(`[XML Collection Handler] Cleaned up temporary file: ${tempFilePath}`, {});
    } catch (error) {
      logger.warn(`[XML Collection Handler] Failed to clean up temporary file ${tempFilePath}: ${error.message}`, {});
    }

    if (!uploadResult.success) {
      const errorMessage = `Failed to upload XML for event ${eventIndex + 1} to ${agent.source}: ${uploadResult.error}`;
      collectionResults.errors.push(errorMessage);

      // Mark this message as failed
      collectionResults.messageResults.push({
        messageId,
        success: false,
        error: errorMessage,
        eventIndex
      });
      collectionResults.failedRecords += records.length;
    } else {
      // Mark this message as successful
      collectionResults.messageResults.push({
        messageId,
        success: true,
        eventIndex
      });
      collectionResults.successfulRecords += records.length;
      collectionResults.filesGenerated++;
      collectionResults.processedFiles.push({
        filename,
        uploadPath: uploadResult.uploadedPath,
        recordCount: records.length,
        eventIndex: eventIndex + 1
      });
    }

  } catch (error) {
    performanceMonitor?.endStep(`Generate Event ${eventIndex + 1} XML File`, { success: false, error: error.message });

    const errorMessage = `Failed to generate XML file for event ${eventIndex + 1}: ${error.message}`;
    collectionResults.errors.push(errorMessage);

    // Mark this message as failed
    collectionResults.messageResults.push({
      messageId,
      success: false,
      error: errorMessage,
      eventIndex
    });
    collectionResults.failedRecords += records.length;
  }
}

module.exports = generateXml;
