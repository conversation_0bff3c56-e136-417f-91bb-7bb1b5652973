const axios = require('axios');
const logger = require('../config/logger');
const { getAgentSettings } = require('../helpers/caching.helper');
const models = require('../models');

// Token cache to store generated tokens
const tokenCache = new Map();

/**
 * Generates or retrieves authentication token
 * @param {object} settings - Agent settings
 * @param {string} agentId - Agent ID for caching
 * @returns {Promise<string>} - Authentication token
 */
const getAuthToken = async (settings, agentId) => {
  // Case 1: Token is directly provided in settings
  if (settings.api_token || settings.token) {
    return settings.api_token || settings.token;
  }

  // Case 2: Token needs to be generated from credentials
  if (settings.token_url && (settings.username || settings.client_id)) {
    const cacheKey = `token_${agentId}`;
    const cachedToken = tokenCache.get(cacheKey);

    // Check if cached token is still valid
    if (cachedToken && cachedToken.expiresAt > Date.now()) {
      logger.debug(`[API Handler] Using cached token for agent ${agentId}`);
      return cachedToken.token;
    }

    try {
      logger.info(`[API Handler] Generating new token for agent ${agentId}`);

      // Prepare token request
      const tokenRequestData = {};

      // Support different authentication methods
      if (settings.grant_type) {
        tokenRequestData.grant_type = settings.grant_type;
      } else {
        tokenRequestData.grant_type = 'client_credentials'; // default
      }

      if (settings.client_id) {
        tokenRequestData.client_id = settings.client_id;
      }

      if (settings.client_secret) {
        tokenRequestData.client_secret = settings.client_secret;
      }

      if (settings.username) {
        tokenRequestData.username = settings.username;
      }

      if (settings.password) {
        tokenRequestData.password = settings.password;
      }

      if (settings.scope) {
        tokenRequestData.scope = settings.scope;
      }

      // Make token request
      const tokenResponse = await axios({
        method: 'POST',
        url: settings.token_url,
        data: tokenRequestData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...(settings.token_headers ? JSON.parse(settings.token_headers) : {})
        },
        // timeout: settings.token_timeout || 30000
      });

      if (tokenResponse.status !== 200) {
        throw new Error(`Token request failed with status ${tokenResponse.status}`);
      }

      const tokenData = tokenResponse.data;
      const token = tokenData.access_token || tokenData.token;

      if (!token) {
        throw new Error('No access token found in response');
      }

      // Calculate expiration time (default to 1 hour if not provided)
      const expiresIn = tokenData.expires_in || 3600; // seconds
      const expiresAt = Date.now() + (expiresIn * 1000) - 60000; // Subtract 1 minute for safety

      // Cache the token
      tokenCache.set(cacheKey, {
        token,
        expiresAt,
        generatedAt: Date.now()
      });

      logger.info(`[API Handler] Token generated successfully for agent ${agentId}, expires in ${expiresIn} seconds`);

      return token;

    } catch (error) {
      logger.error(`[API Handler] Failed to generate token for agent ${agentId}:`, error.message);
      throw new Error(`Token generation failed: ${error.message}`);
    }
  }

  // Case 3: No token configuration found
  // logger.warn(`[API Handler] No token configuration found for agent ${agentId}`);
  return null;
};

/**
 * Transforms template string with data values
 * @param {string} template - Template string with {{field}} placeholders
 * @param {object} data - Data object to extract values from
 * @param {object} context - Additional context data (timestamp, etc.)
 * @returns {string} - Transformed string
 */
const transformTemplate = (template, data, context = {}) => {
  if (typeof template !== 'string') return template;

  return template.replace(/\{\{([^}]+)\}\}/g, (match, field) => {
    // Handle special context fields
    if (field === 'current_timestamp') {
      return new Date().toISOString();
    }

    // Handle nested field access (e.g., Identity.email)
    const fieldParts = field.split('.');
    let value = data;

    for (const part of fieldParts) {
      value = value?.[part];
      if (value === undefined) break;
    }

    return value !== undefined ? value : '';
  });
};

/**
 * Recursively transforms an object using template transformation
 * @param {any} obj - Object to transform
 * @param {object} data - Data for transformation
 * @param {object} context - Context data
 * @returns {any} - Transformed object
 */
const transformObject = (obj, data, context = {}) => {
  if (typeof obj === 'string') {
    return transformTemplate(obj, data, context);
  }

  if (Array.isArray(obj)) {
    return obj.map(item => transformObject(item, data, context));
  }

  if (obj && typeof obj === 'object') {
    const transformed = {};
    for (const [key, value] of Object.entries(obj)) {
      transformed[key] = transformObject(value, data, context);
    }
    return transformed;
  }

  return obj;
};

/**
 * Validates data against mapping rules
 * @param {object} data - Data to validate
 * @param {object} validation - Validation rules
 * @returns {object} - Validation result
 */
const validateData = (data, validation) => {
  const errors = [];

  if (!validation) {
    return { isValid: true, errors: [] };
  }

  // Check required fields
  if (validation.required) {
    for (const field of validation.required) {
      const fieldParts = field.split('.');
      let value = data;
      let currentPath = '';

      for (let i = 0; i < fieldParts.length; i++) {
        const part = fieldParts[i];
        currentPath = currentPath ? `${currentPath}.${part}` : part;

        if (part.includes('*')) {
          // Handle array validation (e.g., employees.*.email)
          const arrayField = part.replace('*', '');
          if (Array.isArray(value)) {
            let hasValidItems = false;
            for (let j = 0; j < value.length; j++) {
              const remainingPath = fieldParts.slice(i + 1).join('.');
              if (remainingPath) {
                // Check nested field in array item
                let nestedValue = value[j];
                const nestedParts = remainingPath.split('.');
                for (const nestedPart of nestedParts) {
                  nestedValue = nestedValue?.[nestedPart];
                }
                if (nestedValue !== undefined && nestedValue !== null && nestedValue !== '') {
                  hasValidItems = true;
                }
              } else if (value[j] && value[j][arrayField] !== undefined && value[j][arrayField] !== null && value[j][arrayField] !== '') {
                hasValidItems = true;
              }
            }
            if (!hasValidItems) {
              errors.push(`Required field ${field} is missing in all array items`);
            }
          } else {
            errors.push(`Expected array for field ${currentPath.replace('.*', '')} but got ${typeof value}`);
          }
          break;
        } else if (!isNaN(part)) {
          // Handle array index (e.g., employees.0.employeeId)
          const index = parseInt(part);
          if (Array.isArray(value)) {
            if (index >= value.length) {
              errors.push(`Array index ${index} out of bounds for field ${currentPath}`);
              break;
            }
            value = value[index];
          } else {
            errors.push(`Expected array for field ${currentPath.replace(`.${part}`, '')} but got ${typeof value}`);
            break;
          }
        } else {
          value = value?.[part];
        }

        // Check if we've reached the end and the value is missing
        if (i === fieldParts.length - 1) {
          if (value === undefined || value === null || value === '') {
            errors.push(`Required field ${field} is missing`);
          }
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Makes HTTP request with retry logic
 * @param {object} config - Request configuration
 * @param {object} performanceMonitor - Performance monitor instance
 * @returns {Promise<object>} - Response data
 */
const makeApiRequest = async (config, performanceMonitor = null) => {
  const { url, method, headers, data,
    // timeout,
    // retries = 3, retryDelay = 1000, retryBackoff = 'linear'
  } = config;

  // for (let attempt = 1; attempt <= retries + 1; attempt++) {
  try {
  const requestStart = Date.now();
  console.log(`########## in makeApiRequest before axios`);
  const response = await axios({
    url,
    method,
    headers,
    data,
    // timeout,
    validateStatus: (status) => status < 600 // Don't throw for HTTP error status codes
  });
  console.log(`########## in makeApiRequest after axios`);

  const requestTime = Date.now() - requestStart;

  // Record API metrics in performance monitor
  performanceMonitor?.logProgress(`API request completed`, {
    // attempt,
    status: response.status,
    requestTime,
    success: response.status >= 200 && response.status < 300,
    url: url.replace(/\/\/.*@/, '//***@') // Hide credentials in logs
  });


  return {
    success: response.status >= 200 && response.status < 300,
    status: response.status,
    data: response.data,
    headers: response.headers,
    requestTime
  };

  } catch (error) {
    throw new Error("Error while making request to given API: ", error.message || error);
    
  //   lastError = error;
  //   const requestTime = Date.now() - (error.config?.metadata?.startTime || Date.now());

  //   // Record failed API metrics
  //   performanceMonitor?.logProgress(`API request failed`, {
  //     // attempt,
  //     error: error.message,
  //     requestTime,
  //     statusCode: error.response?.status,
  //     timeout: error.code === 'ECONNABORTED',
  //     url: url.replace(/\/\/.*@/, '//***@')
  //   });

  //   // logger.warn(`API request attempt ${attempt} failed:`, {
  //   //   error: error.message,
  //   //   url: url.replace(/\/\/.*@/, '//***@'),
  //   //   requestTime
  //   // });

  //   // Don't retry on the last attempt
  //   // if (attempt <= retries) {
  //   //   const delay = retryBackoff === 'exponential' 
  //   //     ? retryDelay * Math.pow(2, attempt - 1)
  //   //     : retryDelay;

  //   //   logger.info(`Retrying API request in ${delay}ms (attempt ${attempt + 1}/${retries + 1})`);
  //   //   await new Promise(resolve => setTimeout(resolve, delay));
  //   // }
  // }
  }

  // All retries failed
  // throw new Error(`API request failed after ${retries + 1} attempts: ${lastError.message}`);
};

/**
 * Collection-based API data sending handler that processes events one by one with efficient token management
 * @param {Object} params - Handler parameters
 * @param {Array} params.eventCollection - Collection of events to process
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Collection processing results
 */
const sendApiData = async ({ eventCollection, agent, mappingConfig, performanceMonitor = null }) => {
  const collectionResults = {
    agentName: agent.name,
    totalEvents: eventCollection.length,
    totalRecords: 0,
    successfulRecords: 0,
    failedRecords: 0,
    apiCalls: 0,
    errors: [],
    messageResults: [] // Track individual message success/failure
  };

  try {
    logger.info(`[API Collection Handler] Starting collection processing for agent: ${agent.name}`, { eventCount: eventCollection.length });

    // Step 1: Load agent settings once for the entire collection
    performanceMonitor?.startStep('Load Configuration', { agentName: agent.name });
    let settings;
    try {
      settings = await getAgentSettings(agent.agent_id);
    } catch (error) {
      logger.error(`[API Collection Handler] Failed to load agent settings for ${agent.name}:`, error.message);

      // Mark all messages as failed due to configuration error
      for (const { messageId } of eventCollection) {
        collectionResults.messageResults.push({
          messageId,
          success: false,
          error: `Configuration error: ${error.message}`
        });
      }

      collectionResults.errors.push(`Failed to load agent settings: ${error.message}`);
      collectionResults.failedRecords = eventCollection.length;
      performanceMonitor?.endStep('Load Configuration', { success: false, error: error.message });
      return collectionResults;
    }
    performanceMonitor?.endStep('Load Configuration', {});

    logger.info(`[API Collection Handler] Loaded configuration for agent: ${agent.name}`, { mapping: agent.mapping });

    // Step 2: Extract parent_ids from events and fetch data in bulk
    performanceMonitor?.startStep('Extract Parent IDs for API', { eventCount: eventCollection.length });
    const parentIds = [];
    const eventToParentIdMapping = new Map(); // Track which parent_id belongs to which event

    for (let eventIndex = 0; eventIndex < eventCollection.length; eventIndex++) {
      const { event, messageId } = eventCollection[eventIndex];
      const parentId = event.parent_id;
      if (parentId) {
        parentIds.push(parentId);
        eventToParentIdMapping.set(eventIndex, { parentId, messageId });
      }
    }

    performanceMonitor?.endStep('Extract Parent IDs for API', { totalParentIds: parentIds.length });

    if (parentIds.length === 0) {
      logger.warn(`[API Collection Handler] No parent_ids found in event collection for agent: ${agent.name}`, {});
      return collectionResults;
    }

    // Step 3: Bulk fetch data from the model specified in agent.schema
    performanceMonitor?.startStep('Bulk Fetch Model Data for API', { modelName: agent.schema, parentIds: parentIds.length });
    const model = models[agent.schema];
    if (!model) {
      const error = `Model '${agent.schema}' not found in models`;
      logger.error(`[API Collection Handler] ${error}`, {});
      collectionResults.errors.push(error);
      return collectionResults;
    }

    let fetchedRecords = [];
    try {
      const primaryKeyField = model.primaryKeyAttribute || 'id';
      const records = await model.findAll({
        where: {
          [primaryKeyField]: parentIds
        }
      });

      // Convert Sequelize instances to plain objects and create lookup map
      fetchedRecords = records.map(record => record.toJSON());

      logger.info(`[API Collection Handler] Fetched ${fetchedRecords.length} records from ${agent.schema} model`, {});
    } catch (error) {
      logger.error(`[API Collection Handler] Error fetching data from ${agent.schema} model:`, error);
      collectionResults.errors.push(`Failed to fetch data from ${agent.schema}: ${error.message}`);
      return collectionResults;
    }

    // Create lookup map for quick access to records by primary key
    const recordLookup = new Map();
    const primaryKeyField = model.primaryKeyAttribute || 'id';
    fetchedRecords.forEach(record => {
      recordLookup.set(record[primaryKeyField], record);
    });
    performanceMonitor?.endStep('Bulk Fetch Model Data for API', { totalRecords: fetchedRecords.length });

    // Step 4: Process each event one by one with efficient token management
    for (let eventIndex = 0; eventIndex < eventCollection.length; eventIndex++) {
      const eventMapping = eventToParentIdMapping.get(eventIndex);
      if (eventIndex % 2000 == 0) {
        console.log(`########## before continue eventMapping:`);
        console.log(eventMapping);
        console.log(!eventMapping);
      }

      if (!eventMapping) {
        continue; // Skip events without parent_id
      }

      if (eventIndex % 2000 == 0) {
        console.log(`########## eventMapping:`); console.log(eventMapping);
      }

      const { parentId, messageId } = eventMapping;
      const eventData = recordLookup.get(parentId);

      if (eventIndex % 2000 == 0) {
        console.log(`########## eventData:`); console.log(eventData);
      }

      if (!eventData) {
        logger.warn(`[API Collection Handler] No data found for parent_id: ${parentId}`, {});
        collectionResults.messageResults.push({
          messageId,
          success: false,
          error: `No data found for parent_id: ${parentId}`
        });
        collectionResults.failedRecords++;
        continue;
      }

      try {
        logger.info(`[API Handler] Processing event ${eventIndex + 1}/${eventCollection.length} for agent: ${agent.name}`, { messageId });
        const context = {
          timestamp: new Date().toISOString(),
          messageId,
          eventIndex: eventIndex + 1,
          totalEvents: eventCollection.length
        };

        const recordCount = Array.isArray(eventData) ? eventData.length : 1;
        collectionResults.totalRecords += recordCount;

        // Prepare data for transformation based on mapping type
        let dataForTransformation;
        let transformedData;

        // Check if this is an array-based mapping (like externalPartnerApi)
        const isArrayMapping = mappingConfig.dataTransform.properties.employees &&
          Array.isArray(mappingConfig.dataTransform.properties.employees);

        if (isArrayMapping) {
          // For array-based mappings, handle multiple records
          if (Array.isArray(eventData)) {
            // Transform each item in the array
            dataForTransformation = eventData.map(item => ({ Identity: item }));
          } else {
            // Single item, wrap in array
            dataForTransformation = [{ Identity: eventData }];
          }

          // Transform data according to mapping
          const employeeTemplate = mappingConfig.dataTransform.properties.employees[0];
          const transformedEmployees = dataForTransformation.map(data =>
            transformObject(employeeTemplate, data, context)
          );

          // Build the complete transformed structure
          transformedData = {
            employees: transformedEmployees,
          };

        } else {
          // For single object mappings (like apiOutbound)
          if (Array.isArray(eventData)) {
            // Take the first item if it's an array
            dataForTransformation = { Identity: eventData[0] };
          } else {
            // Single object, wrap in Identity key
            dataForTransformation = { Identity: eventData };
          }

          // Transform data according to mapping
          transformedData = transformObject(mappingConfig.dataTransform.properties, dataForTransformation, context);
        }

        logger.info(`[API Handler] Transformed data for event ${eventIndex + 1}:`, JSON.stringify(transformedData, null, 2));

        // Validate transformed data
        const validation = validateData(transformedData, mappingConfig.validation);
        if (!validation.isValid) {
          logger.error(`[API Handler] Data validation failed for event ${eventIndex + 1}:`, validation.errors);

          collectionResults.failedRecords += recordCount;
          collectionResults.errors.push(`Event ${eventIndex + 1} validation failed: ${validation.errors.join(', ')}`);

          collectionResults.messageResults.push({
            messageId,
            success: false,
            error: `Validation failed: ${validation.errors.join(', ')}`
          });

          continue; // Skip to next event
        }

        logger.info(`[API Handler] Data validation passed for event ${eventIndex + 1}`);

        // Get authentication token (will use cached token if still valid)
        const authToken = await getAuthToken(settings, agent.agent_id);

        // Prepare API request
        const contextWithAuth = {
          ...settings,
          ...context,
          auth_token: authToken,
          api_token: authToken // For backward compatibility
        };

        const apiConfig = {
          url: settings.api_url || settings.url,
          method: mappingConfig.apiConfig.method,
          headers: transformObject(mappingConfig.apiConfig.headers, {}, contextWithAuth),
          data: transformedData,
          timeout: mappingConfig.apiConfig.timeout,
          retries: mappingConfig.apiConfig.retries,
          retryDelay: mappingConfig.apiConfig.retryDelay,
          retryBackoff: mappingConfig.apiConfig.retryBackoff
        };

        // Make API call
        logger.info(`[API Handler] Making API call to ${apiConfig.url} for event ${eventIndex + 1}`);

        const response = await makeApiRequest(apiConfig, performanceMonitor);
        collectionResults.apiCalls++;

        if (response.success) {
          collectionResults.successfulRecords += recordCount;
          logger.info(`[API Handler] Successfully sent ${recordCount} records for event ${eventIndex + 1}`);

          collectionResults.messageResults.push({
            messageId,
            success: true,
            status: response.status
          });
        } else {
          collectionResults.failedRecords += recordCount;
          collectionResults.errors.push(`Event ${eventIndex + 1} API call failed with status ${response.status}`);
          logger.error(`[API Handler] API call failed for event ${eventIndex + 1}:`, response.data);

          collectionResults.messageResults.push({
            messageId,
            success: false,
            error: `API call failed with status ${response.status}`,
            status: response.status
          });
        }

      } catch (eventError) {
        logger.error(`[API Handler] Error processing event ${eventIndex + 1} for agent ${agent.name}:`, eventError);

        const recordCount = Array.isArray(eventData) ? eventData.length : 1;
        collectionResults.failedRecords += recordCount;
        collectionResults.errors.push(`Event ${eventIndex + 1} processing error: ${eventError.message}`);

        collectionResults.messageResults.push({
          messageId,
          success: false,
          error: `Processing error: ${eventError.message}`
        });
      }
    }

    logger.info(`[API Collection Handler] Collection processing completed for agent: ${agent.name}`, {
      totalEvents: collectionResults.totalEvents,
      totalRecords: collectionResults.totalRecords,
      successfulRecords: collectionResults.successfulRecords,
      failedRecords: collectionResults.failedRecords,
      apiCalls: collectionResults.apiCalls
    });

    return collectionResults;

  } catch (error) {
    logger.error(`[API Collection Handler] Error processing collection for agent: ${agent.name}:`, error.message || error, {});
    collectionResults.errors.push(error.message || error);
    collectionResults.failedRecords = collectionResults.totalRecords;
    return collectionResults;
  }
};

module.exports = sendApiData;
